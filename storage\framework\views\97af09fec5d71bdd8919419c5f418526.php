<?php $__env->startSection('title', 'عرض المستخدم'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0 font-size-18">عرض المستخدم</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.users.index')); ?>">المستخدمين</a></li>
                        <li class="breadcrumb-item active">عرض المستخدم</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- User Details Card -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">تفاصيل المستخدم</h4>
                        <div>
                            <a href="<?php echo e(route('admin.users.edit', $user->id)); ?>" class="btn btn-primary btn-sm me-2">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- User Avatar -->
                        <div class="col-md-3 text-center mb-4">
                            <div class="avatar-lg mx-auto mb-3">
                                <?php if($user->avatar): ?>
                                    <img src="<?php echo e(asset('storage/' . $user->avatar)); ?>" alt="<?php echo e($user->name); ?>" 
                                         class="rounded-circle img-thumbnail" style="width: 120px; height: 120px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="avatar-title rounded-circle bg-primary text-white" style="width: 120px; height: 120px; font-size: 48px; line-height: 120px;">
                                        <?php echo e(strtoupper(substr($user->name, 0, 1))); ?>

                                    </div>
                                <?php endif; ?>
                            </div>
                            <h5 class="font-size-16 mb-1"><?php echo e($user->name); ?></h5>
                            <p class="text-muted mb-2"><?php echo e($user->email); ?></p>
                            <div class="mt-3">
                                <?php if($user->is_active): ?>
                                    <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير نشط</span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- User Information -->
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">الاسم الكامل:</label>
                                    <p class="text-muted mb-0"><?php echo e($user->name); ?></p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">البريد الإلكتروني:</label>
                                    <p class="text-muted mb-0"><?php echo e($user->email); ?></p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">رقم الهاتف:</label>
                                    <p class="text-muted mb-0"><?php echo e($user->phone ?? 'غير محدد'); ?></p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">المنصب:</label>
                                    <p class="text-muted mb-0"><?php echo e($user->position ?? 'غير محدد'); ?></p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">القسم:</label>
                                    <p class="text-muted mb-0"><?php echo e($user->department ?? 'غير محدد'); ?></p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">الراتب:</label>
                                    <p class="text-muted mb-0"><?php echo e($user->salary ? number_format($user->salary) . ' ريال' : 'غير محدد'); ?></p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                                    <p class="text-muted mb-0"><?php echo e($user->created_at->format('Y-m-d H:i')); ?></p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">آخر تحديث:</label>
                                    <p class="text-muted mb-0"><?php echo e($user->updated_at->format('Y-m-d H:i')); ?></p>
                                </div>
                                <?php if($user->email_verified_at): ?>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">تاريخ تأكيد البريد:</label>
                                    <p class="text-muted mb-0"><?php echo e($user->email_verified_at->format('Y-m-d H:i')); ?></p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- User Roles -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="font-size-14 mb-3">الأدوار المخصصة:</h5>
                            <?php if($user->roles && $user->roles->count() > 0): ?>
                                <div class="d-flex flex-wrap gap-2">
                                    <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge bg-info font-size-12"><?php echo e($role->name); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">لا يوجد أدوار مخصصة لهذا المستخدم</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- User Permissions -->
                    <?php if($user->getAllPermissions()->count() > 0): ?>
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="font-size-14 mb-3">الصلاحيات:</h5>
                            <div class="d-flex flex-wrap gap-2">
                                <?php $__currentLoopData = $user->getAllPermissions(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="badge bg-secondary font-size-11"><?php echo e($permission->name); ?></span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.avatar-title {
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Auth\Providers/../resources/views/users/show.blade.php ENDPATH**/ ?>