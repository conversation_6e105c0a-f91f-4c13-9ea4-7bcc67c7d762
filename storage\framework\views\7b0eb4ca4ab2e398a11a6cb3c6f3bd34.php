<?php $__env->startPush('styles'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <nav class="flex mb-2" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <span class="text-gray-700 text-sm font-medium">إدارة النظام</span>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                            <span class="text-gray-500 text-sm">المستخدمين</span>
                        </div>
                    </li>
                </ol>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-users mr-3"></i>
                إدارة المستخدمين
            </h1>
            <p class="text-sm text-gray-600 mt-1">إدارة المستخدمين وأدوارهم في النظام</p>
        </div>
        <div>
            <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200" onclick="openModal('addUserModal')">
                <i class="fas fa-plus mr-2"></i>
                إضافة مستخدم جديد
            </button>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg font-medium text-gray-900">قائمة المستخدمين</h3>
                <p class="text-sm text-gray-600 mt-1">إدارة المستخدمين وأدوارهم</p>
            </div>
        </div>
    </div>
    <div class="p-6">
        <!-- DataTable Controls -->
        <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
            <div class="flex flex-col sm:flex-row gap-4">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="globalSearch" placeholder="البحث في المستخدمين..." class="w-full sm:w-64 pl-3 pr-10 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Role Filter -->
                <select id="roleFilter" class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الأدوار</option>
                    <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($role->name); ?>"><?php echo e($role->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>

                <!-- Status Filter -->
                <select id="statusFilter" class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الحالات</option>
                    <option value="1">نشط</option>
                    <option value="0">غير نشط</option>
                </select>
            </div>

         
        </div>

        <!-- DataTable Container -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table id="usersTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">#</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الاسم</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">البريد الإلكتروني</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الأدوار</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الحالة</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    </tbody>
                </table>
            </div>

            <!-- DataTable Info and Pagination will be inserted here by DataTables -->
            <div class="bg-gray-50 px-6 py-3 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700" id="tableInfo">
                        <!-- DataTable info will be inserted here -->
                    </div>
                    <div id="tablePagination">
                        <!-- DataTable pagination will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div id="addUserModal" class="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-0 border-0 w-11/12 max-w-3xl shadow-2xl rounded-lg bg-white">
        <!-- Modal Header -->
        <div class="flex justify-between items-center px-6 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-t-lg">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-user-plus text-sm"></i>
                </div>
                <h3 class="text-lg font-semibold">إضافة مستخدم جديد</h3>
            </div>
            <button type="button" class="text-white hover:text-gray-200 transition-colors duration-200" onclick="closeModal('addUserModal')">
                <i class="fas fa-times text-lg"></i>
            </button>
        </div>
        <!-- Modal Body -->
        <div class="px-6 py-6 max-h-96 overflow-y-auto">
            <form id="addUserForm" action="<?php echo e(route('users.store')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                            <i class="fas fa-user text-blue-600"></i>
                            المعلومات الأساسية
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">الاسم <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-user text-gray-400 text-sm"></i>
                                    </div>
                                    <input type="text" class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" id="name" name="name" required placeholder="أدخل اسم المستخدم">
                                </div>
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-envelope text-gray-400 text-sm"></i>
                                    </div>
                                    <input type="email" class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" id="email" name="email" required placeholder="<EMAIL>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Password Information -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                            <i class="fas fa-lock text-blue-600"></i>
                            كلمة المرور
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-key text-gray-400 text-sm"></i>
                                    </div>
                                    <input type="password" class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" id="password" name="password" required placeholder="أدخل كلمة المرور">
                                </div>
                            </div>
                            <div>
                                <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-key text-gray-400 text-sm"></i>
                                    </div>
                                    <input type="password" class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" id="password_confirmation" name="password_confirmation" required placeholder="أعد إدخال كلمة المرور">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Roles Section -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                            <i class="fas fa-users-cog text-blue-600"></i>
                            الأدوار والصلاحيات
                        </h4>
                        <div class="bg-white border border-gray-200 rounded-lg p-4 max-h-40 overflow-y-auto">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors duration-200">
                                        <input type="checkbox" name="roles[]" value="<?php echo e($role->name); ?>" id="role_<?php echo e($role->id); ?>" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="role_<?php echo e($role->id); ?>" class="mr-3 text-sm text-gray-700 cursor-pointer flex-1">
                                            <span class="font-medium"><?php echo e($role->name); ?></span>
                                            <span class="text-gray-500 text-xs block">(<?php echo e($role->guard_name); ?>)</span>
                                        </label>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
            <div class="flex justify-end space-x-3">
                <button type="button" class="px-6 py-2 bg-gray-500 text-white text-sm font-medium rounded-lg hover:bg-gray-600 transition-colors duration-200 flex items-center gap-2" onclick="closeModal('addUserModal')">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="submit" form="addUserForm" class="px-6 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    حفظ المستخدم
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit User Modal -->
<div id="editUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-medium text-gray-900">تعديل المستخدم</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('editUserModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="editUserForm" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            <div class="mt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="edit_name" class="block text-sm font-medium text-gray-700 mb-2">الاسم <span class="text-red-500">*</span></label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_name" name="name" required>
                    </div>
                    <div>
                        <label for="edit_email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني <span class="text-red-500">*</span></label>
                        <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_email" name="email" required>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                        <label for="edit_password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الجديدة</label>
                        <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_password" name="password">
                        <p class="text-xs text-gray-500 mt-1">اتركها فارغة إذا كنت لا تريد تغييرها</p>
                    </div>
                    <div>
                        <label for="edit_password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور</label>
                        <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_password_confirmation" name="password_confirmation">
                    </div>
                </div>

                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-3">الأدوار:</label>
                    <div class="edit-roles-container grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3">
                        <!-- Roles will be loaded dynamically -->
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-2 pt-4">
                <button type="button" class="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-md hover:bg-gray-600" onclick="closeModal('editUserModal')">إلغاء</button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">تحديث</button>
            </div>
        </form>
    </div>
</div>

<!-- Assign Roles Modal -->
<div id="assignRolesModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-medium text-gray-900">إدارة أدوار المستخدم</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('assignRolesModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="assignRolesForm" method="POST">
            <?php echo csrf_field(); ?>
            <div class="mt-4">
                <div class="assign-roles-container grid grid-cols-1 md:grid-cols-2 gap-2 max-h-60 overflow-y-auto border border-gray-200 rounded-md p-3">
                    <!-- Roles will be loaded dynamically -->
                </div>
            </div>
            <div class="flex justify-end space-x-2 pt-4">
                <button type="button" class="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-md hover:bg-gray-600" onclick="closeModal('assignRolesModal')">إلغاء</button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">حفظ الأدوار</button>
            </div>
        </form>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- DataTables CDN -->
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<!-- Sweet Alert -->
<!-- SweetAlert -->
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>

<script>
// Enhanced Modal Functions
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        
        // Focus on first input field
        setTimeout(() => {
            const firstInput = modal.querySelector('input:not([type="hidden"]):not([disabled])');
            if (firstInput) {
                firstInput.focus();
            }
        }, 100);
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
        
        // Clear any form validation errors
        modal.querySelectorAll('.text-red-500').forEach(el => el.remove());
        modal.querySelectorAll('.border-red-500').forEach(el => {
            el.classList.remove('border-red-500');
            el.classList.add('border-gray-300');
        });
    }
}

    // Function to show form submission feedback
    function showFormFeedback(form, isLoading) {
        const submitBtn = form.find('button[type="submit"]');
        const inputs = form.find('input, select, textarea');
        
        if (isLoading) {
            submitBtn.prop('disabled', true);
            inputs.prop('disabled', true);
            form.addClass('opacity-75');
        } else {
            submitBtn.prop('disabled', false);
            inputs.prop('disabled', false);
            form.removeClass('opacity-75');
        }
    }

$(document).ready(function() {
    // Prevent all forms from default submission to ensure modal behavior
    $('form').on('submit', function(e) {
        // Only prevent default if this form has an AJAX handler
        if ($(this).attr('id') && ($(this).attr('id').includes('addUser') || $(this).attr('id').includes('editUser') || $(this).attr('id').includes('assignRoles'))) {
            e.preventDefault();
            return false;
        }
    });
    // Initialize DataTable
    var table = $('#usersTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: "<?php echo e(route('users.data')); ?>",
        columns: [
            {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
            {data: 'name', name: 'name'},
            {data: 'email', name: 'email'},
            {data: 'roles', name: 'roles', orderable: false},
            {data: 'status_badge', name: 'is_active'},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                className: 'hidden'
            },
            {
                extend: 'pdf',
                className: 'hidden'
            },
            {
                extend: 'print',
                className: 'hidden'
            }
        ],
        language: {
            processing: "جاري التحميل...",
            search: "البحث:",
            lengthMenu: "عرض _MENU_ عنصر",
            info: "عرض _START_ إلى _END_ من _TOTAL_ عنصر",
            infoEmpty: "عرض 0 إلى 0 من 0 عنصر",
            infoFiltered: "(تم التصفية من _MAX_ عنصر)",
            paginate: {
                first: "الأول",
                last: "الأخير",
                next: "التالي",
                previous: "السابق"
            }
        }
    });

    // Enhanced Search and Filter Functionality
    $('#globalSearch').on('keyup', function() {
        table.search(this.value).draw();
    });

    $('#roleFilter').on('change', function() {
        var role = this.value;
        table.column(3).search(role).draw();
    });

    $('#statusFilter').on('change', function() {
        var status = this.value;
        table.column(4).search(status).draw();
    });

    // Export button functionality
    $('#exportExcel').on('click', function() {
        table.button('.buttons-excel').trigger();
    });

    $('#exportPdf').on('click', function() {
        table.button('.buttons-pdf').trigger();
    });

    $('#printTable').on('click', function() {
        table.button('.buttons-print').trigger();
    });

    // Store roles data for JavaScript use
    var rolesData = <?php echo json_encode($roles ?? [], 15, 512) ?>;

    // Handle Add User Form Submission
    $('#addUserForm').on('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...').prop('disabled', true);

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                closeModal('addUserModal');
                $('#addUserForm')[0].reset();
                swal("تم الحفظ!", "تم إضافة المستخدم بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON?.errors;
                var errorMessage = 'حدث خطأ أثناء إضافة المستخدم:\n';
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += errors[key][0] + '\n';
                    });
                }
                swal("خطأ!", errorMessage, "error");
            },
            complete: function() {
                // Reset button state
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Handle Edit User Form Submission
    $('#editUserForm').on('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري التحديث...').prop('disabled', true);
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'PUT',
            data: $(this).serialize(),
            success: function(response) {
                closeModal('editUserModal');
                swal("تم التحديث!", "تم تحديث بيانات المستخدم بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON?.errors;
                var errorMessage = 'حدث خطأ أثناء تحديث المستخدم:\n';
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += errors[key][0] + '\n';
                    });
                }
                swal("خطأ!", errorMessage, "error");
            },
            complete: function() {
                // Reset button state
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Handle Assign Roles Form Submission
    $('#assignRolesForm').on('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...').prop('disabled', true);
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                closeModal('assignRolesModal');
                swal("تم الحفظ!", "تم تحديث أدوار المستخدم بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                swal("خطأ!", "حدث خطأ أثناء تحديث الأدوار.", "error");
            },
            complete: function() {
                // Reset button state
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Edit user function
    window.editUser = function(id) {
        var showUrl = "<?php echo e(route('users.show', ':id')); ?>".replace(':id', id);
        $.get(showUrl, function(data) {
            $('#edit_name').val(data.name);
            $('#edit_email').val(data.email);
            $('#edit_password').val('');
            $('#edit_password_confirmation').val('');
            
            var updateUrl = "<?php echo e(route('users.update', ':id')); ?>".replace(':id', id);
            $('#editUserForm').attr('action', updateUrl);
            
            // Load roles for editing
            loadRolesForEdit(data.roles || []);
            
            openModal('editUserModal');
        }).fail(function() {
            swal("خطأ!", "حدث خطأ أثناء تحميل بيانات المستخدم.", "error");
        });
    };

    // Load roles for edit modal
    function loadRolesForEdit(userRoles) {
        var rolesHtml = '';

        rolesData.forEach(function(role) {
            rolesHtml += '<div class="flex items-center">';
            rolesHtml += '<input type="checkbox" name="roles[]" value="' + role.name + '" id="edit_role_' + role.id + '" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"';

            // Check if user has this role
            var hasRole = false;
            if (userRoles && Array.isArray(userRoles)) {
                hasRole = userRoles.some(function(r) {
                    return r.name === role.name;
                });
            }
            if (hasRole) rolesHtml += ' checked';

            rolesHtml += '>';
            rolesHtml += '<label for="edit_role_' + role.id + '" class="mr-2 text-sm text-gray-700">';
            rolesHtml += role.name + ' <span class="text-gray-500 text-xs">(' + role.guard_name + ')</span>';
            rolesHtml += '</label>';
            rolesHtml += '</div>';
        });

        $('.edit-roles-container').html(rolesHtml);
    }

    // Assign roles to user
    window.assignRoles = function(id) {
        var rolesUrl = "<?php echo e(route('users.roles', ':id')); ?>".replace(':id', id);
        $.get(rolesUrl, function(data) {
            var assignUrl = "<?php echo e(route('users.roles.assign', ':id')); ?>".replace(':id', id);
            $('#assignRolesForm').attr('action', assignUrl);

            var rolesHtml = '';

            rolesData.forEach(function(role) {
                rolesHtml += '<div class="flex items-center">';
                rolesHtml += '<input type="checkbox" name="roles[]" value="' + role.name + '" id="assign_role_' + role.id + '" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"';

                // Check if user has this role
                var hasRole = false;
                if (data.userRoles && Array.isArray(data.userRoles)) {
                    hasRole = data.userRoles.includes(role.name);
                }
                if (hasRole) rolesHtml += ' checked';

                rolesHtml += '>';
                rolesHtml += '<label for="assign_role_' + role.id + '" class="mr-2 text-sm text-gray-700">';
                rolesHtml += role.name + ' <span class="text-gray-500 text-xs">(' + role.guard_name + ')</span>';
                rolesHtml += '</label>';
                rolesHtml += '</div>';
            });

            $('.assign-roles-container').html(rolesHtml);

            openModal('assignRolesModal');
        }).fail(function() {
            swal("خطأ!", "حدث خطأ أثناء تحميل أدوار المستخدم.", "error");
        });
    };

    // Change user status (activate/deactivate)
    window.changeStatus = function(id, action) {
        var url = "<?php echo e(route('users.activate', ':id')); ?>".replace(':id', id);
        var message = "تم تفعيل المستخدم بنجاح.";
        var title = "تم التفعيل!";
        
        if (action === 'deactivate') {
            url = "<?php echo e(route('users.deactivate', ':id')); ?>".replace(':id', id);
            message = "تم إلغاء تفعيل المستخدم بنجاح.";
            title = "تم إلغاء التفعيل!";
        }
        
        $.ajax({
            url: url,
            type: 'POST',
            data: {
                _token: "<?php echo e(csrf_token()); ?>"
            },
            success: function(response) {
                swal(title, message, "success");
                table.draw();
            },
            error: function(xhr) {
                swal("خطأ!", "حدث خطأ أثناء تغيير حالة المستخدم.", "error");
            }
        });
    };

    // Reset user password
    window.resetPassword = function(id) {
        swal({
            title: "إعادة تعيين كلمة المرور",
            text: "هل أنت متأكد من إعادة تعيين كلمة المرور لهذا المستخدم؟",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، أعد تعيين!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false,
            closeOnCancel: false
        }, function(isConfirm) {
            if (isConfirm) {
                var resetUrl = "<?php echo e(route('users.reset-password', ':id')); ?>".replace(':id', id);
                $.ajax({
                    url: resetUrl,
                    type: 'POST',
                    data: {
                        _token: "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(response) {
                        swal("تم إعادة التعيين!", "تم إعادة تعيين كلمة المرور بنجاح.", "success");
                        table.draw();
                    },
                    error: function(xhr) {
                        swal("خطأ!", "حدث خطأ أثناء إعادة تعيين كلمة المرور.", "error");
                    }
                });
            } else {
                swal("تم الإلغاء", "لم يتم إعادة تعيين كلمة المرور.", "error");
            }
        });
    };

    // Delete user
    window.deleteUser = function(id) {
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false,
            closeOnCancel: false
        }, function(isConfirm) {
            if (isConfirm) {
                var deleteUrl = "<?php echo e(route('users.destroy', ':id')); ?>".replace(':id', id);
                $.ajax({
                    url: deleteUrl,
                    type: 'DELETE',
                    data: {
                        _token: "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(response) {
                        swal("تم الحذف!", "تم حذف المستخدم بنجاح.", "success");
                        table.draw();
                    },
                    error: function(xhr) {
                        swal("خطأ!", "حدث خطأ أثناء حذف المستخدم.", "error");
                    }
                });
            } else {
                swal("تم الإلغاء", "لم يتم حذف المستخدم.", "error");
            }
        });
    };

    // Enhanced modal functions with cleanup
    window.closeModalWithCleanup = function(modalId) {
        closeModal(modalId);
        
        // Clean up forms based on modal
        if (modalId === 'addUserModal') {
            $('#addUserForm')[0].reset();
        } else if (modalId === 'editUserModal') {
            $('#editUserForm')[0].reset();
            $('.edit-roles-container').html('');
        } else if (modalId === 'assignRolesModal') {
            $('#assignRolesForm')[0].reset();
            $('.assign-roles-container').html('');
        }
    };

    // Close modal when clicking outside
    $(document).on('click', '.fixed.inset-0', function(e) {
        if (e.target === this) {
            var modalId = $(this).attr('id');
            closeModalWithCleanup(modalId);
        }
    });

    // Close modal with Escape key
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            var openModal = $('.fixed.inset-0:not(.hidden)');
            if (openModal.length > 0) {
                var modalId = openModal.attr('id');
                closeModalWithCleanup(modalId);
            }
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Auth\Providers/../resources/views/users/index.blade.php ENDPATH**/ ?>