<?php

namespace Modules\Orders\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\MenuItem;
use App\Models\Table;
use App\Models\User;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Orders\Http\Requests\StoreOrderRequest;
use Modules\Orders\Services\OrderService;
use Modules\Kitchen\Services\KitchenService;

class POSController extends Controller
{
    protected $orderService;
    protected $kitchenService;

    public function __construct(OrderService $orderService, KitchenService $kitchenService)
    {
        $this->orderService = $orderService;
        $this->kitchenService = $kitchenService;
    }

    /**
     * Display POS dashboard
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get recent orders for the current branch with transaction data
        $recentOrders = Order::where('branch_id', $user->branch_id)
            ->with(['customer', 'table', 'orderItems', 'transaction'])
            ->orderBy('created_at', 'desc')
            ->limit(20) // Increased limit for better overview
            ->get();

        return view('orders::pos.index', compact('recentOrders'));
    }

    /**
     * Show the order creation form
     */
    public function create()
    {
        $user = Auth::user();
        
        // Get customers for the current branch/tenant
        $customers = Customer::where('tenant_id', $user->tenant_id)
            ->where('is_active', true)
            ->orderBy('first_name')
            ->get();

        // Get available tables for the current branch
        $tables = Table::where('branch_id', $user->branch_id)
            ->where('is_active', true)
            ->orderBy('table_number')
            ->get();

        // Get delivery personnel (users with delivery role)
        $deliveryPersonnel = User::where('tenant_id', $user->tenant_id)
            ->where('is_active', true)
            ->whereHas('roles', function($query) {
                $query->where('name', 'delivery');
            })
            ->orderBy('name')
            ->get();

        // Get menu items for the current branch
        $menuItems = MenuItem::whereHas('menu', function($query) use ($user) {
                $query->where('branch_id', $user->branch_id)
                      ->where('is_active', true);
            })
            ->with(['category', 'variants', 'addons', 'menu'])
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        // If no menu items found, try to get all active menu items (fallback)
        if ($menuItems->isEmpty()) {
            $menuItems = MenuItem::with(['category', 'variants', 'addons', 'menu'])
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->limit(20) // Limit for demo purposes
                ->get();
        }

        // Group menu items by category name, handling null categories
        $menuCategories = $menuItems->groupBy(function($item) {
            return $item->category ? $item->category->name : 'Uncategorized';
        });

        return view('orders::pos.create', compact(
            'customers', 
            'tables', 
            'deliveryPersonnel', 
            'menuItems',
            'menuCategories'
        ));
    }

    /**
     * Store a new order from POS (Web form submission)
     */
    public function store(StoreOrderRequest $request)
    {
        try {
            \Log::info('POS Order Request:', $request->validated());
            $user = Auth::user();
            
            // Add branch and tenant info
            $data = $request->validated();
            $data['branch_id'] = $user->branch_id;
            $data['tenant_id'] = $user->tenant_id;
            $data['cashier_id'] = $user->id;
            
            // Ensure order status is confirmed for KOT creation
            if (!isset($data['status']) || $data['status'] !== 'cancelled') {
                $data['status'] = 'confirmed';
            }

            // Create the order (KOT creation is handled automatically in OrderService)
            $order = $this->orderService->createOrder($data);

            // Check if KOT was created (for response message)
            $kotCreated = $order->hasActiveKot();

            // Handle printing if requested
            $printTriggered = false;
            if ($request->input('with_print', false) && $kotCreated) {
                try {
                    // Trigger kitchen printer (this would integrate with actual printer)
                    // For now, we'll just log it
                    \Log::info('KOT print triggered for order ' . $order->id);
                    $printTriggered = true;
                } catch (\Exception $e) {
                    \Log::error('Failed to print KOT for order ' . $order->id . ': ' . $e->getMessage());
                }
            }

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Order created successfully' . ($kotCreated ? ' with KOT orders' : '') . ($printTriggered ? ' and sent to printer' : ''),
                    'data' => $order,
                    'kot_created' => $kotCreated,
                    'print_triggered' => $printTriggered,
                    'redirect' => route('pos.orders.kot', $order)
                ]);
            }

            return redirect()->route('pos.orders.kot', $order)->with('success', 'Order created successfully');

        } catch (\Exception $e) {
            \Log::error('POS Order creation failed: ' . $e->getMessage(), [
                'request_data' => $request->validated(),
                'trace' => $e->getTraceAsString()
            ]);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create order: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Failed to create order: ' . $e->getMessage());
        }
    }

    /**
     * Generate and display KOT
     */
    public function generateKOT(Order $order)
    {
        $user = Auth::user();

        // Verify order belongs to user's branch
        if ($order->branch_id !== $user->branch_id) {
            abort(404);
        }

        $order->load(['orderItems.menuItem', 'customer', 'table', 'branch']);

        return view('orders::pos.kot', compact('order'));
    }

    /**
     * Generate bill for order
     */
    public function generateBill(Order $order)
    {
        $user = Auth::user();

        // Verify order belongs to user's branch
        if ($order->branch_id !== $user->branch_id) {
            abort(404);
        }

        $order->load(['orderItems.menuItem', 'customer', 'table', 'branch', 'transaction']);

        if (request()->expectsJson()) {
            // Generate bill HTML for printing
            $billHtml = view('orders::pos.bill', compact('order'))->render();

            return response()->json([
                'success' => true,
                'bill_html' => $billHtml,
                'message' => 'Bill generated successfully'
            ]);
        }

        return view('orders::pos.bill', compact('order'));
    }

    /**
     * Show payment interface for order
     */
    public function showPayment(Order $order)
    {
        $user = Auth::user();

        // Verify order belongs to user's branch
        if ($order->branch_id !== $user->branch_id) {
            abort(404);
        }

        $order->load(['orderItems.menuItem', 'customer', 'table', 'branch', 'transaction']);

        // Get available payment methods
        $paymentMethods = \Modules\Transaction\Models\PaymentMethod::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        return view('orders::pos.payment', compact('order', 'paymentMethods'));
    }

    /**
     * Print KOT
     */
    public function printKOT(Order $order)
    {
        $user = Auth::user();

        // Verify order belongs to user's branch
        if ($order->branch_id !== $user->branch_id) {
            abort(404);
        }

        $order->load(['orderItems.menuItem', 'customer', 'table', 'branch']);

        // Here you would integrate with actual printer
        // For now, we'll return a success response
        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'KOT sent to kitchen printer successfully'
            ]);
        }

        return view('orders::pos.kot-print', compact('order'));
    }

    /**
     * Get menu item variants and addons for AJAX requests (Web)
     */
    public function getMenuItemAddons(MenuItem $menuItem)
    {
        $user = Auth::user();

        // Verify menu item belongs to user's branch
        if (!$menuItem->menu || $menuItem->menu->branch_id !== $user->branch_id) {
            return response()->json(['error' => 'Menu item not found'], 404);
        }

        $menuItem->load(['variants', 'addons']);

        return response()->json([
            'variants' => $menuItem->variants,
            'addons' => $menuItem->addons
        ]);
    }

    /**
     * Get menu item details with variants and addons (AJAX for Web)
     */
    public function getMenuItemDetails(MenuItem $menuItem)
    {
        $user = Auth::user();

        // Verify menu item belongs to user's branch
        if (!$menuItem->menu || $menuItem->menu->branch_id !== $user->branch_id) {
            return response()->json(['error' => 'Menu item not found'], 404);
        }

        $menuItem->load(['variants' => function($query) {
            $query->where('is_active', true)->orderBy('sort_order');
        }, 'addons' => function($query) {
            $query->orderBy('addon_group_name')->orderBy('sort_order');
        }]);

        return response()->json([
            'id' => $menuItem->id,
            'name' => $menuItem->name,
            'description' => $menuItem->description,
            'base_price' => $menuItem->base_price,
            'image' => $menuItem->image,
            'variants' => $menuItem->variants->map(function($variant) {
                return [
                    'id' => $variant->id,
                    'name' => $variant->name,
                    'price_modifier' => $variant->price_modifier,
                    'is_default' => $variant->is_default,
                ];
            }),
            'addons' => $menuItem->addons->groupBy('addon_group_name')->map(function($group, $groupName) {
                return [
                    'group_name' => $groupName,
                    'items' => $group->map(function($addon) {
                        return [
                            'id' => $addon->id,
                            'name' => $addon->name,
                            'price' => $addon->price,
                            'is_required' => $addon->is_required,
                            'max_quantity' => $addon->max_quantity,
                        ];
                    })
                ];
            })->values()
        ]);
    }

    /**
     * Calculate order totals for AJAX requests (Web)
     */
    public function calculateOrderTotals(Request $request)
    {
        $items = $request->input('items', []);
        $orderDiscount = $request->input('order_discount', []);
        $taxRate = $request->input('tax_rate', 0.1); // Default 10% tax

        $subtotal = 0;
        $totalDiscount = 0;

        foreach ($items as $item) {
            $itemSubtotal = ($item['unit_price'] ?? 0) * ($item['quantity'] ?? 1);
            
            // Add addon costs
            if (isset($item['addons']) && is_array($item['addons'])) {
                foreach ($item['addons'] as $addon) {
                    $itemSubtotal += ($addon['unit_price'] ?? 0) * ($addon['quantity'] ?? 1);
                }
            }

            $subtotal += $itemSubtotal;
        }

        // Apply order-level discount
        if (!empty($orderDiscount)) {
            if ($orderDiscount['type'] === 'percentage') {
                $orderDiscountAmount = $subtotal * ($orderDiscount['value'] / 100);
            } else {
                $orderDiscountAmount = $orderDiscount['value'];
            }
            $subtotal -= $orderDiscountAmount;
            $totalDiscount += $orderDiscountAmount;
        }

        $taxAmount = $subtotal * $taxRate;
        $total = $subtotal + $taxAmount;

        return response()->json([
            'subtotal' => round($subtotal, 2),
            'tax_amount' => round($taxAmount, 2),
            'total_discount' => round($totalDiscount, 2),
            'total' => round($total, 2)
        ]);
    }
}
