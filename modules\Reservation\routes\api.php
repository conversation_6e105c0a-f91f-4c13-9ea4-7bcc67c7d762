<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Reservation\Http\Controllers\Api\ReservationController;
use Modules\Reservation\Http\Controllers\Api\AreaController;
use Modules\Reservation\Http\Controllers\Api\TableController;
use Modules\Reservation\Http\Controllers\Api\QRCodeController;
use Modules\Reservation\Http\Controllers\Api\WaiterRequestController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Public API Routes (no authentication required)
Route::prefix('reservation')->group(function () {
    // QR Code validation (public access for customer scanning)
    Route::prefix('qr')->group(function () {
        Route::post('validate', [QRCodeController::class, 'validate']);
        Route::get('table/{tableId}', [QRCodeController::class, 'getTableQR']);
    });
});

// Authenticated API Routes
Route::middleware(['auth:sanctum'])->prefix('reservation')->group(function () {
    // Reservations API
    Route::apiResource('reservations', ReservationController::class);
    Route::post('reservations/{id}/confirm', [ReservationController::class, 'confirm']);
    Route::get('reservations/status/{status}', [ReservationController::class, 'getByStatus']);
    
    // Areas API
    Route::apiResource('areas', AreaController::class);
    Route::get('areas/{id}/tables', [AreaController::class, 'getTables']);
    
    // Tables API
    Route::apiResource('tables', TableController::class);
    Route::post('tables/{id}/generate-qr', [TableController::class, 'generateQR']);
    Route::post('tables/{id}/regenerate-qr', [TableController::class, 'regenerateQR']);
    Route::get('tables/area/{areaId}', [TableController::class, 'getByArea']);
    Route::get('tables/available', [TableController::class, 'getAvailable']);
    
    // QR Codes API (authenticated operations)
    Route::prefix('qr')->group(function () {
        Route::post('generate', [QRCodeController::class, 'generate']);
    });
    
    // Waiter Requests API
    Route::apiResource('waiter-requests', WaiterRequestController::class);
    Route::patch('waiter-requests/{id}/complete', [WaiterRequestController::class, 'complete']);
    Route::patch('waiter-requests/{id}/cancel', [WaiterRequestController::class, 'cancel']);
    Route::get('waiter-requests/status/{status}', [WaiterRequestController::class, 'getByStatus']);
    
    // DataTable API Routes (for AJAX DataTables)
    Route::prefix('datatable')->group(function () {
        Route::get('reservations', [ReservationController::class, 'dataTable']);
        Route::get('areas', [AreaController::class, 'dataTable']);
        Route::get('tables', [TableController::class, 'dataTable']);
        Route::get('waiter-requests', [WaiterRequestController::class, 'dataTable']);
    });
    
    // Utility/Dropdown Routes
    Route::prefix('utils')->group(function () {
        Route::get('customers', [ReservationController::class, 'getCustomers']);
        Route::get('areas', [AreaController::class, 'getList']);
        Route::get('tables', [TableController::class, 'getList']);
        Route::get('areas/{id}/tables', [AreaController::class, 'getAvailableTablesByArea']);
        Route::get('waiters', [WaiterRequestController::class, 'getWaiters']);
        Route::get('reservation-statuses', [ReservationController::class, 'getStatuses']);
    });
});

// DataTable API Routes (for frontend DataTables)
// Route::middleware(['auth'])->group(function () {
//     Route::get('/reservations', [WebReservationController::class, 'reservationsDataTable']);
//     Route::get('/areas', [WebReservationController::class, 'areasDataTable']);
//     Route::get('/tables', [WebReservationController::class, 'tablesDataTable']);
//     Route::get('/waiter-requests', [WebReservationController::class, 'waiterRequestsDataTable']);
//     Route::get('/api/waiter-requests-cards', [WebReservationController::class, 'waiterRequestsCards']);
//     Route::get('/waiter-requests-cards', [WebReservationController::class, 'waiterRequestsCards']);

//     // CRUD Routes for Reservations
//     Route::post('/reservations', [WebReservationController::class, 'reservationsStore'])->name('reservations.store');
//     Route::get('/reservations/{id}', [WebReservationController::class, 'reservationsShow'])->name('reservations.show');
//     Route::get('/reservations/{id}/edit', [WebReservationController::class, 'reservationsEdit'])->name('reservations.edit');
//     Route::put('/reservations/{id}', [WebReservationController::class, 'reservationsUpdate'])->name('reservations.update');
//     Route::delete('/reservations/{id}', [WebReservationController::class, 'reservationsDestroy'])->name('reservations.destroy');
//     Route::post('/reservations/{id}/confirm', [WebReservationController::class, 'reservationsConfirm'])->name('reservations.confirm');

//     // CRUD Routes for Areas
//     Route::post('/areas', [WebReservationController::class, 'areasStore'])->name('areas.store');
//     Route::get('/areas/{id}', [WebReservationController::class, 'areasShow'])->name('areas.show');
//     Route::get('/areas/{id}/edit', [WebReservationController::class, 'areasEdit'])->name('areas.edit');
//     Route::put('/areas/{id}', [WebReservationController::class, 'areasUpdate'])->name('areas.update');
//     Route::delete('/areas/{id}', [WebReservationController::class, 'areasDestroy'])->name('areas.destroy');
//     Route::get('/areas/{id}/tables', [WebReservationController::class, 'getAreaTables'])->name('areas.tables');

//     // CRUD Routes for Waiter Requests
//     Route::post('/waiter-requests', [WebReservationController::class, 'waiterRequestsStore'])->name('waiter-requests.store');
//     Route::get('/waiter-requests/{id}', [WebReservationController::class, 'waiterRequestsShow'])->name('waiter-requests.show');
//     Route::get('/waiter-requests/{id}/edit', [WebReservationController::class, 'waiterRequestsEdit'])->name('waiter-requests.edit');
//     Route::put('/waiter-requests/{id}', [WebReservationController::class, 'waiterRequestsUpdate'])->name('waiter-requests.update');
//     Route::delete('/waiter-requests/{id}', [WebReservationController::class, 'waiterRequestsDestroy'])->name('waiter-requests.destroy');
//     Route::patch('/waiter-requests/{id}/complete', [WebReservationController::class, 'waiterRequestsComplete'])->name('waiter-requests.complete');
//     Route::patch('/waiter-requests/{id}/cancel', [WebReservationController::class, 'waiterRequestsCancel'])->name('waiter-requests.cancel');

//     // Dropdown/List Routes
//     Route::get('/customers', [WebReservationController::class, 'getCustomers'])->name('customers.list');
//     Route::get('/areas-list', [WebReservationController::class, 'getAreas'])->name('areas.list');
//     Route::get('/tables-list', [WebReservationController::class, 'getTables'])->name('tables.list');
//     Route::get('/waiters', [WebReservationController::class, 'getWaiters'])->name('waiters.list');
//     Route::get('/reservation-statuses', [WebReservationController::class, 'getReservationStatuses'])->name('reservation-statuses.list');
// });