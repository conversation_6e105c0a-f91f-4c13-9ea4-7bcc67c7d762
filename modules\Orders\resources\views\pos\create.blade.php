@extends('layouts.master')
@section('title', 'POS Management')

@push('head')
<!-- <PERSON><PERSON> Manifest -->
<link rel="manifest" href="{{ asset('pos-manifest.json') }}">
<meta name="theme-color" content="#2563eb">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="default">
<meta name="apple-mobile-web-app-title" content="POS System">
@endpush

@push('styles')
<!-- SweetAlert2 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<!-- Material Design Icons -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css">
<!-- POS Offline Styles -->
<link rel="stylesheet" href="{{ asset('assets/css/pos.css') }}">
<!-- Custom Styles -->
<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .menu-item:hover {
        transform: translateY(-2px);
    }
    
    .category-tab.active {
        background-color: #2563eb !important;
        color: white !important;
        border-color: #2563eb !important;
    }

    .order-item {
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 8px;
        background: #f9fafb;
    }

    .addon-item {
        background: #f3f4f6;
        border-radius: 4px;
        padding: 6px 8px;
        margin: 2px 0;
        font-size: 0.875rem;
    }

    .discount-section {
        background: #fef3c7;
        border: 1px solid #f59e0b;
        border-radius: 6px;
        padding: 8px;
        margin-top: 8px;
    }

    .calculation-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
    }

    .total-row {
        font-weight: bold;
        font-size: 1.1rem;
        border-top: 2px solid #e5e7eb;
        padding-top: 8px;
        margin-top: 8px;
    }
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-50 p-4">
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50" style="display: none;">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <p class="mt-4 text-gray-600">Processing order...</p>
    </div>

    <!-- Header -->
    <div class="bg-white rounded-xl p-6 mb-6 shadow-sm border border-gray-200">
        <div class="flex justify-between items-center flex-wrap gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">POS System</h1>
                <p class="text-gray-600">Create New Order</p>
            </div>
            <div class="flex items-center gap-4">
                <div id="connectionStatus" class="inline-flex items-center gap-2 px-4 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    <i class="mdi mdi-wifi"></i>
                    <span>Online</span>
                </div>
                <a href="{{ route('pos.index') }}" class="inline-flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
                    <i class="mdi mdi-arrow-left mr-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Main POS Interface -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Menu Section (Increased width) -->
        <div class="lg:col-span-2 bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div class="flex justify-between items-center flex-wrap mb-6">
                <h2 class="text-xl font-bold text-gray-900">Menu Items</h2>
                <div class="flex items-center gap-4">
                    <button id="gridViewBtn" class="p-2 bg-blue-600 text-white rounded-lg">
                        <i class="mdi mdi-view-grid"></i>
                    </button>
                    <button id="listViewBtn" class="p-2 bg-gray-300 text-gray-700 rounded-lg">
                        <i class="mdi mdi-view-list"></i>
                    </button>
                </div>
            </div>

            <!-- Search -->
            <input type="text" id="menuSearch" class="w-full px-4 py-2 border border-gray-300 rounded-lg mb-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Search menu items...">
            
            <!-- Category Tabs -->
             <div class="flex flex-wrap gap-2 mb-6">
                 <button class="category-tab px-4 py-2 rounded-lg border border-gray-300 bg-blue-600 text-white cursor-pointer transition-all hover:bg-gray-50 hover:border-blue-300" data-category="all">All Items</button>
                 @foreach($menuCategories as $categoryName => $items)
                     <button class="category-tab px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 cursor-pointer transition-all hover:bg-gray-50 hover:border-blue-300" data-category="{{ $categoryName }}">{{ $categoryName }}</button>
                 @endforeach
             </div>

             <!-- Menu Items Grid -->
             <div id="menuGrid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                 @foreach($menuItems as $item)
                     <div class="menu-item bg-white border border-gray-200 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-md hover:border-blue-300 hover:translate-y-0.5" 
                          data-item-id="{{ $item->id }}" 
                          data-item-name="{{ $item->name }}" 
                          data-item-price="{{ $item->base_price }}" 
                          data-category="{{ $item->category ? $item->category->name : 'Uncategorized' }}">
                        
                        @if($item->image && !empty($item->image))
                            <img src="{{ asset($item->image) }}" alt="{{ $item->name }}" class="w-full h-32 object-cover rounded-lg mb-3 bg-gray-100 flex items-center justify-center" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="w-full h-32 object-cover rounded-lg mb-3 bg-gray-100 flex items-center justify-center" style="display: none;">
                                <i class="mdi mdi-food text-4xl text-gray-400"></i>
                            </div>
                        @else
                            <div class="w-full h-32 object-cover rounded-lg mb-3 bg-gray-100 flex items-center justify-center">
                                <i class="mdi mdi-food text-4xl text-gray-400"></i>
                            </div>
                        @endif
                        
                        <div class="relative">
                            <!-- Badges for variants/addons -->
                            @if($item->variants->count() > 0 || $item->addons->count() > 0)
                                <div class="absolute -top-2 -right-2 flex gap-1">
                                    @if($item->variants->count() > 0)
                                        <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">V</span>
                                    @endif
                                    @if($item->addons->count() > 0)
                                        <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">A</span>
                                    @endif
                                </div>
                            @endif
                            
                            <h3 class="font-semibold text-gray-900 mb-1">{{ $item->name }}</h3>
                            @if($item->short_description)
                                <p class="text-sm text-gray-600 mb-2 line-clamp-2">{{ $item->short_description }}</p>
                            @endif
                            <div class="text-lg font-bold text-blue-600">${{ number_format((float)$item->base_price, 2) }}</div>
                            <button class="add-to-order-btn mt-2 w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors" 
                                    data-item-id="{{ $item->id }}"
                                    data-item-name="{{ $item->name }}" 
                                    data-item-price="{{ $item->base_price }}">
                                <i class="mdi mdi-plus mr-1"></i>
                                Add to Order
                            </button>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Order Section (Decreased width) -->
        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 sticky top-4" style="max-height: calc(100vh - 2rem); overflow-y: auto;">
            <h2 class="text-xl font-bold text-gray-900 mb-4">Current Order</h2>
            
            <!-- Order Type Selection -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Order Type</label>
                <select id="orderType" class="w-full px-3 py-2 border border-gray-300 rounded-lg mb-4 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="dine_in">Dine In</option>
                    <option value="takeaway">Takeaway</option>
                    <option value="delivery">Delivery</option>
                </select>
            </div>

            <!-- Customer Selection -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Customer (Optional)</label>
                <select id="customerId" class="w-full px-3 py-2 border border-gray-300 rounded-lg mb-4 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">Select Customer</option>
                    @foreach($customers as $customer)
                        <option value="{{ $customer->id }}">{{ $customer->first_name }} {{ $customer->last_name }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Table Selection (for dine-in) -->
            <div id="tableSelection" class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Table</label>
                <select id="tableId" class="w-full px-3 py-2 border border-gray-300 rounded-lg mb-4 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">Select Table</option>
                    @foreach($tables as $table)
                        <option value="{{ $table->id }}">Table {{ $table->table_number }} ({{ $table->capacity }} seats)</option>
                    @endforeach
                </select>
            </div>

            <!-- Delivery Personnel Selection (for delivery) -->
            <div id="deliverySelection" class="mb-4" style="display: none;">
                <label class="block text-sm font-medium text-gray-700 mb-2">Delivery Personnel</label>
                <select id="deliveryPersonnelId" class="w-full px-3 py-2 border border-gray-300 rounded-lg mb-4 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">Select Delivery Personnel</option>
                    @foreach($deliveryPersonnel as $personnel)
                        <option value="{{ $personnel->id }}">{{ $personnel->name }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Order Items -->
            <div id="orderItems" class="mb-4">
                <div class="text-center text-gray-500 py-8">
                    <i class="mdi mdi-cart-outline text-4xl text-gray-400 mb-2"></i>
                    <p>No items in order</p>
                </div>
            </div>

            <!-- Order-level Discount -->
            <div class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                    <label class="text-sm font-medium text-gray-700">Order Discount</label>
                    <button id="toggleOrderDiscount" class="text-sm text-blue-600 hover:text-blue-800">
                        <i class="mdi mdi-plus"></i> Add
                    </button>
                </div>
                <div id="orderDiscountSection" style="display: none;">
                    <div class="grid grid-cols-2 gap-2 mb-2">
                        <select id="orderDiscountType" class="px-2 py-1 border border-gray-300 rounded text-sm">
                            <option value="fixed">Fixed ($)</option>
                            <option value="percentage">Percentage (%)</option>
                        </select>
                        <input type="number" id="orderDiscountValue" placeholder="0" min="0" step="0.01" class="px-2 py-1 border border-gray-300 rounded text-sm">
                    </div>
                    <button id="removeOrderDiscount" class="text-xs text-red-600 hover:text-red-800">Remove Discount</button>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="border-t border-gray-200 pt-4 mt-4">
                <div class="space-y-2 text-sm">
                    <div class="calculation-row">
                        <span>Subtotal:</span>
                        <span id="orderSubtotal">$0.00</span>
                    </div>
                    <div class="calculation-row" id="orderDiscountRow" style="display: none;">
                        <span>Order Discount:</span>
                        <span id="orderDiscountAmount" class="text-red-600">-$0.00</span>
                    </div>
                    <div class="calculation-row">
                        <span>Tax:</span>
                        <span id="orderTax">$0.00</span>
                    </div>
                    <div class="calculation-row total-row">
                        <span>Total:</span>
                        <span id="orderTotal">$0.00</span>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="mt-6 space-y-3">
                    <!-- KOT Buttons Row -->
                    <div class="grid grid-cols-2 gap-2">
                        <button id="kotBtn" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-lg font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed text-sm" disabled>
                            <i class="mdi mdi-chef-hat mr-1"></i>
                            KOT
                        </button>
                        <button id="kotPrintBtn" class="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-3 rounded-lg font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed text-sm" disabled>
                            <i class="mdi mdi-printer mr-1"></i>
                            KOT&Print
                        </button>
                    </div>

                    <!-- Bill and Pay Buttons Row -->
                    <div class="grid grid-cols-2 gap-2">
                        <button id="billBtn" class="bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded-lg font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed text-sm" disabled>
                            <i class="mdi mdi-receipt mr-1"></i>
                            Bill
                        </button>
                        <button id="payBtn" class="bg-orange-600 hover:bg-orange-700 text-white py-2 px-3 rounded-lg font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed text-sm" disabled>
                            <i class="mdi mdi-credit-card mr-1"></i>
                            Pay
                        </button>
                    </div>

                    <!-- Clear Order Button -->
                    <button id="clearOrderBtn" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors">
                        <i class="mdi mdi-delete mr-2"></i>
                        Clear Order
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Variants & Add-ons Modal -->
<div id="variantsAddonsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-xl p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
            <h3 id="modalTitle" class="text-xl font-bold text-gray-900">Customize Item</h3>
            <button id="closeModal" class="text-gray-500 hover:text-gray-700">
                <i class="mdi mdi-close text-2xl"></i>
            </button>
        </div>

        <div id="modalContent">
            <!-- Variants Section -->
            <div id="variantsSection" class="mb-6" style="display: none;">
                <h4 class="font-semibold text-gray-900 mb-3">Select Variant</h4>
                <div id="variantsList" class="space-y-2"></div>
            </div>

            <!-- Add-ons Section -->
            <div id="addonsSection" class="mb-6" style="display: none;">
                <h4 class="font-semibold text-gray-900 mb-3">Add-ons</h4>
                <div id="addonsList" class="space-y-2"></div>
            </div>

            <!-- Quantity and Notes -->
            <div class="mb-6">
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                        <input type="number" id="itemQuantity" value="1" min="1" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Unit Price</label>
                        <input type="number" id="itemPrice" step="0.01" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    </div>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Notes (Optional)</label>
                    <textarea id="itemNotes" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="Special instructions..."></textarea>
                </div>
            </div>

            <!-- Price Summary -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span>Item Price:</span>
                        <span id="modalItemPrice">$0.00</span>
                    </div>
                    <div class="flex justify-between" id="modalAddonsRow" style="display: none;">
                        <span>Add-ons:</span>
                        <span id="modalAddonsPrice">$0.00</span>
                    </div>
                    <div class="flex justify-between font-semibold border-t pt-2">
                        <span>Total:</span>
                        <span id="modalTotalPrice">$0.00</span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-3">
                <button id="cancelModal" class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-lg transition-colors">
                    Cancel
                </button>
                <button id="addToOrderModal" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                    Add to Order
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- POS Offline JavaScript Modules -->
<script src="{{ asset('assets/js/pos/pos-storage.js') }}"></script>
<script src="{{ asset('assets/js/pos/pos-sync.js') }}"></script>
<script src="{{ asset('assets/js/pos/pos-ui.js') }}"></script>
<script src="{{ asset('assets/js/pos/pos-core.js') }}"></script>

<script>
$(document).ready(function() {
    // Global variables
    let currentOrder = {
        items: [],
        subtotal: 0,
        tax_amount: 0,
        discount_amount: 0,
        total_amount: 0,
        order_discount: null
    };
    let currentModalItem = null;
    const TAX_RATE = 0.10; // 10% tax rate

    // Initialize
    updateOrderDisplay();
    setupEventListeners();

    function setupEventListeners() {
        // Category filtering
        $('.category-tab').on('click', function() {
            $('.category-tab').removeClass('active');
            $(this).addClass('active');
            
            const category = $(this).data('category');
            filterMenuItems(category);
        });

        // Menu search
        $('#menuSearch').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            filterMenuItems($('.category-tab.active').data('category'), searchTerm);
        });

        // Add to order button
        $(document).on('click', '.add-to-order-btn', function() {
            const itemId = $(this).data('item-id');
            const itemName = $(this).data('item-name');
            const itemPrice = parseFloat($(this).data('item-price'));
            
            // Check if item has variants or addons
            checkItemVariantsAddons(itemId, itemName, itemPrice);
        });

        // Order type change
        $('#orderType').on('change', function() {
            const orderType = $(this).val();
            toggleOrderTypeFields(orderType);
        });

        // Order discount toggle
        $('#toggleOrderDiscount').on('click', function() {
            $('#orderDiscountSection').toggle();
            if ($('#orderDiscountSection').is(':visible')) {
                $(this).html('<i class="mdi mdi-minus"></i> Remove');
            } else {
                $(this).html('<i class="mdi mdi-plus"></i> Add');
                removeOrderDiscount();
            }
        });

        // Order discount calculation
        $('#orderDiscountType, #orderDiscountValue').on('change input', function() {
            calculateOrderDiscount();
        });

        // Remove order discount
        $('#removeOrderDiscount').on('click', function() {
            removeOrderDiscount();
        });

        // Modal events
        $('#closeModal, #cancelModal').on('click', function() {
            closeModal();
        });

        // Modal price calculations
        $('#itemQuantity, #itemPrice').on('change input', function() {
            calculateModalPrices();
        });

        // Add to order from modal
        $('#addToOrderModal').on('click', function() {
            addItemToOrder();
        });

        // KOT Button - Send to kitchen only
        $('#kotBtn').on('click', function() {
            sendKOT(false); // false = no print
        });

        // KOT & Print Button - Send to kitchen and print
        $('#kotPrintBtn').on('click', function() {
            sendKOT(true); // true = with print
        });

        // Bill Button - Generate and print bill
        $('#billBtn').on('click', function() {
            generateBill();
        });

        // Pay Button - Open payment interface
        $('#payBtn').on('click', function() {
            openPaymentInterface();
        });

        // Clear order
        $('#clearOrderBtn').on('click', function() {
            clearOrder();
        });
    }

    function filterMenuItems(category, searchTerm = '') {
        $('.menu-item').each(function() {
            const itemCategory = $(this).data('category');
            const itemName = $(this).data('item-name').toLowerCase();
            
            const categoryMatch = category === 'all' || itemCategory === category;
            const searchMatch = searchTerm === '' || itemName.includes(searchTerm);
            
            if (categoryMatch && searchMatch) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }

    function toggleOrderTypeFields(orderType) {
        $('#tableSelection').toggle(orderType === 'dine_in');
        $('#deliverySelection').toggle(orderType === 'delivery');
    }

    function checkItemVariantsAddons(itemId, itemName, itemPrice) {
        // Show loading
        $('#loadingOverlay').show();

        $.ajax({
            url: `/pos/menu-items/${itemId}/addons`,
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('#loadingOverlay').hide();
                
                if (response.variants.length > 0 || response.addons.length > 0) {
                    showVariantsAddonsModal(itemId, itemName, itemPrice, response.variants, response.addons);
                } else {
                    // Add directly to order
                    addSimpleItemToOrder(itemId, itemName, itemPrice);
                }
            },
            error: function(xhr) {
                $('#loadingOverlay').hide();
                Swal.fire('Error', 'Failed to load item details', 'error');
            }
        });
    }

    function showVariantsAddonsModal(itemId, itemName, itemPrice, variants, addons) {
        currentModalItem = {
            id: itemId,
            name: itemName,
            base_price: itemPrice,
            variants: variants,
            addons: addons
        };

        $('#modalTitle').text(`Customize ${itemName}`);
        $('#itemPrice').val(itemPrice);
        $('#itemQuantity').val(1);
        $('#itemNotes').val('');

        // Setup variants
        if (variants.length > 0) {
            $('#variantsSection').show();
            const variantsList = $('#variantsList');
            variantsList.empty();

            variants.forEach(variant => {
                const variantHtml = `
                    <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="variant" value="${variant.id}" data-price="${variant.price}" data-name="${variant.name}" class="mr-3">
                        <div class="flex-1">
                            <div class="font-medium">${variant.name}</div>
                            <div class="text-sm text-gray-600">$${parseFloat(variant.price).toFixed(2)}</div>
                        </div>
                    </label>
                `;
                variantsList.append(variantHtml);
            });

            // Auto-select first variant and update price
            $('input[name="variant"]:first').prop('checked', true);
            $('#itemPrice').val($('input[name="variant"]:checked').data('price'));
        } else {
            $('#variantsSection').hide();
        }

        // Setup addons
        if (addons.length > 0) {
            $('#addonsSection').show();
            const addonsList = $('#addonsList');
            addonsList.empty();

            addons.forEach(addon => {
                const addonHtml = `
                    <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="checkbox" name="addons" value="${addon.id}" data-price="${addon.price}" data-name="${addon.name}" class="mr-3">
                        <div class="flex-1">
                            <div class="font-medium">${addon.name}</div>
                            <div class="text-sm text-gray-600">+$${parseFloat(addon.price).toFixed(2)}</div>
                        </div>
                        <input type="number" class="addon-quantity w-16 px-2 py-1 border border-gray-300 rounded text-sm" value="1" min="1" disabled>
                    </label>
                `;
                addonsList.append(addonHtml);
            });

            // Enable/disable quantity inputs
            $('input[name="addons"]').on('change', function() {
                $(this).closest('label').find('.addon-quantity').prop('disabled', !$(this).is(':checked'));
                calculateModalPrices();
            });

            $('.addon-quantity').on('change', function() {
                calculateModalPrices();
            });
        } else {
            $('#addonsSection').hide();
        }

        // Variant change event
        $('input[name="variant"]').on('change', function() {
            $('#itemPrice').val($(this).data('price'));
            calculateModalPrices();
        });

        calculateModalPrices();
        $('#variantsAddonsModal').show();
    }

    function calculateModalPrices() {
        const quantity = parseInt($('#itemQuantity').val()) || 1;
        const unitPrice = parseFloat($('#itemPrice').val()) || 0;
        const itemTotal = unitPrice * quantity;

        // Calculate addons total
        let addonsTotal = 0;
        $('input[name="addons"]:checked').each(function() {
            const addonPrice = parseFloat($(this).data('price'));
            const addonQuantity = parseInt($(this).closest('label').find('.addon-quantity').val()) || 1;
            addonsTotal += addonPrice * addonQuantity * quantity;
        });

        const finalTotal = itemTotal + addonsTotal;

        // Update display
        $('#modalItemPrice').text(`$${itemTotal.toFixed(2)}`);
        $('#modalAddonsPrice').text(`$${addonsTotal.toFixed(2)}`);
        $('#modalTotalPrice').text(`$${finalTotal.toFixed(2)}`);

        // Show/hide rows
        $('#modalAddonsRow').toggle(addonsTotal > 0);
    }

    function addItemToOrder() {
        const quantity = parseInt($('#itemQuantity').val()) || 1;
        const unitPrice = parseFloat($('#itemPrice').val()) || 0;
        const notes = $('#itemNotes').val();

        // Get selected variant
        let variant = null;
        const selectedVariant = $('input[name="variant"]:checked');
        if (selectedVariant.length > 0) {
            variant = {
                id: selectedVariant.val(),
                name: selectedVariant.data('name'),
                price: parseFloat(selectedVariant.data('price'))
            };
        }

        // Get selected addons
        const addons = [];
        $('input[name="addons"]:checked').each(function() {
            const addonQuantity = parseInt($(this).closest('label').find('.addon-quantity').val()) || 1;
            addons.push({
                addon_id: $(this).val(),
                addon_name: $(this).data('name'),
                quantity: addonQuantity,
                unit_price: parseFloat($(this).data('price')),
                total_price: parseFloat($(this).data('price')) * addonQuantity
            });
        });

        // Get discount
        let discount = null;

        // Calculate item subtotal
        const itemTotal = unitPrice * quantity;
        const addonsTotal = addons.reduce((sum, addon) => sum + (addon.total_price * quantity), 0);

        const subtotal = itemTotal + addonsTotal;

        // Create order item
        const orderItem = {
            menu_item_id: currentModalItem.id,
            menu_item_name: currentModalItem.name,
            quantity: quantity,
            unit_price: unitPrice,
            subtotal: subtotal,
            notes: notes,
            variant_id: variant ? variant.id : null,
            variant_name: variant ? variant.name : null,
            addons: addons,
            discount: discount
        };

        currentOrder.items.push(orderItem);
        updateOrderDisplay();
        closeModal();

     
    }

    function addSimpleItemToOrder(itemId, itemName, itemPrice) {
        const orderItem = {
            menu_item_id: itemId,
            menu_item_name: itemName,
            quantity: 1,
            unit_price: itemPrice,
            subtotal: itemPrice,
            notes: null,
            variant_id: null,
            variant_name: null,
            addons: [],
            discount: null
        };

        currentOrder.items.push(orderItem);
        updateOrderDisplay();

      
    }

    function updateOrderDisplay() {
        const orderItemsContainer = $('#orderItems');
        
        if (currentOrder.items.length === 0) {
            orderItemsContainer.html(`
                <div class="text-center text-gray-500 py-8">
                    <i class="mdi mdi-cart-outline text-4xl text-gray-400 mb-2"></i>
                    <p>No items in order</p>
                </div>
            `);
            // Disable all action buttons when no items
            $('#kotBtn, #kotPrintBtn, #billBtn, #payBtn').prop('disabled', true);
        } else {
            let itemsHtml = '';
            currentOrder.items.forEach((item, index) => {
                const addonsHtml = item.addons.map(addon => 
                    `<div class="addon-item">+ ${addon.addon_name} (${addon.quantity}x) - $${(addon.total_price * item.quantity).toFixed(2)}</div>`
                ).join('');

                itemsHtml += `
                    <div class="order-item" data-index="${index}">
                        <div class="flex justify-between items-start mb-2">
                            <div class="flex-1">
                                <h4 class="font-medium">${item.menu_item_name}</h4>
                                ${item.variant_name ? `<small class="text-gray-600">Variant: ${item.variant_name}</small>` : ''}
                                ${item.notes ? `<small class="text-gray-600 block">Notes: ${item.notes}</small>` : ''}
                            </div>
                            <button class="remove-item text-red-600 hover:text-red-800 ml-2" data-index="${index}">
                                <i class="mdi mdi-delete"></i>
                            </button>
                        </div>
                        ${addonsHtml}
                        <div class="flex justify-between items-center mt-2">
                            <div class="flex items-center gap-2">
                                <button class="decrease-qty bg-gray-200 hover:bg-gray-300 w-8 h-8 rounded flex items-center justify-center" data-index="${index}">-</button>
                                <span class="w-8 text-center">${item.quantity}</span>
                                <button class="increase-qty bg-gray-200 hover:bg-gray-300 w-8 h-8 rounded flex items-center justify-center" data-index="${index}">+</button>
                            </div>
                            <div class="text-right">
                                <div class="font-medium">$${item.subtotal.toFixed(2)}</div>
                                <small class="text-gray-600">$${item.unit_price.toFixed(2)} each</small>
                            </div>
                        </div>
                    </div>
                `;
            });

            orderItemsContainer.html(itemsHtml);
            // Enable action buttons when items are present
            $('#kotBtn, #kotPrintBtn, #billBtn, #payBtn').prop('disabled', false);

            // Bind quantity change events
            $('.increase-qty').on('click', function() {
                const index = $(this).data('index');
                changeItemQuantity(index, 1);
            });

            $('.decrease-qty').on('click', function() {
                const index = $(this).data('index');
                changeItemQuantity(index, -1);
            });

            $('.remove-item').on('click', function() {
                const index = $(this).data('index');
                removeItem(index);
            });
        }

        calculateOrderTotals();
    }

    function changeItemQuantity(index, change) {
        const item = currentOrder.items[index];
        const newQuantity = item.quantity + change;
        
        if (newQuantity > 0) {
            item.quantity = newQuantity;
            
            // Recalculate item subtotal
            const itemTotal = item.unit_price * item.quantity;
            const addonsTotal = item.addons.reduce((sum, addon) => sum + (addon.total_price * item.quantity), 0);

            item.subtotal = itemTotal + addonsTotal;
            updateOrderDisplay();
        }
    }

    function removeItem(index) {
        currentOrder.items.splice(index, 1);
        updateOrderDisplay();
    }

    function calculateOrderTotals() {
        // Calculate subtotal from all items
        currentOrder.subtotal = currentOrder.items.reduce((sum, item) => sum + item.subtotal, 0);

        // Calculate order-level discount
        let orderDiscountAmount = 0;
        if (currentOrder.order_discount) {
            if (currentOrder.order_discount.type === 'fixed') {
                orderDiscountAmount = Math.min(currentOrder.order_discount.value, currentOrder.subtotal);
            } else if (currentOrder.order_discount.type === 'percentage') {
                orderDiscountAmount = (currentOrder.subtotal * currentOrder.order_discount.value) / 100;
            }
            currentOrder.order_discount.amount = orderDiscountAmount;
        }

        // Calculate tax (on subtotal minus order discount)
        const taxableAmount = currentOrder.subtotal - orderDiscountAmount;
        currentOrder.tax_amount = taxableAmount * TAX_RATE;

        // Calculate total
        currentOrder.discount_amount = orderDiscountAmount;
        currentOrder.total_amount = currentOrder.subtotal - currentOrder.discount_amount + currentOrder.tax_amount;

        // Update display
        $('#orderSubtotal').text(`$${currentOrder.subtotal.toFixed(2)}`);
        $('#orderTax').text(`$${currentOrder.tax_amount.toFixed(2)}`);
        $('#orderTotal').text(`$${currentOrder.total_amount.toFixed(2)}`);

        // Show/hide discount row
        if (orderDiscountAmount > 0) {
            $('#orderDiscountRow').show();
            $('#orderDiscountAmount').text(`-$${orderDiscountAmount.toFixed(2)}`);
        } else {
            $('#orderDiscountRow').hide();
        }
    }

    function calculateOrderDiscount() {
        const discountType = $('#orderDiscountType').val();
        const discountValue = parseFloat($('#orderDiscountValue').val()) || 0;

        if (discountValue > 0) {
            currentOrder.order_discount = {
                type: discountType,
                value: discountValue
            };
        } else {
            currentOrder.order_discount = null;
        }

        calculateOrderTotals();
    }

    function removeOrderDiscount() {
        currentOrder.order_discount = null;
        $('#orderDiscountSection').hide();
        $('#toggleOrderDiscount').html('<i class="mdi mdi-plus"></i> Add');
        $('#orderDiscountValue').val('');
        calculateOrderTotals();
    }

    function closeModal() {
        $('#variantsAddonsModal').hide();
        currentModalItem = null;
    }

    function clearOrder() {
        Swal.fire({
            title: 'Clear Order?',
            text: 'This will remove all items from the current order.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, clear it!'
        }).then((result) => {
            if (result.isConfirmed) {
                currentOrder = {
                    items: [],
                    subtotal: 0,
                    tax_amount: 0,
                    discount_amount: 0,
                    total_amount: 0,
                    order_discount: null
                };
                removeOrderDiscount();
                updateOrderDisplay();
                
                Swal.fire('Cleared!', 'Your order has been cleared.', 'success');
            }
        });
    }

    // Global variable to store current order ID after creation
    let currentOrderId = null;

    function sendKOT(withPrint = false) {
        if (currentOrder.items.length === 0) {
            Swal.fire('Error', 'Please add items to your order', 'error');
            return;
        }

        const orderType = $('#orderType').val();
        const customerId = $('#customerId').val() || null;
        const tableId = $('#tableId').val() || null;
        const deliveryPersonnelId = $('#deliveryPersonnelId').val() || null;

        // Validate required fields based on order type
        if (orderType === 'delivery' && !deliveryPersonnelId) {
            Swal.fire('Error', 'Please select delivery personnel for delivery orders', 'error');
            return;
        }

        const orderData = {
            order_type: orderType,
            customer_id: customerId,
            table_id: tableId,
            delivery_man_id: deliveryPersonnelId,
            subtotal: currentOrder.subtotal,
            tax_amount: currentOrder.tax_amount,
            discount_amount: currentOrder.discount_amount,
            total_amount: currentOrder.total_amount,
            items: currentOrder.items,
            order_discount: currentOrder.order_discount,
            with_print: withPrint
        };

        $('#loadingOverlay').show();

        $.ajax({
            url: '{{ route("pos.orders.store") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Content-Type': 'application/json'
            },
            data: JSON.stringify(orderData),
            success: function(response) {
                $('#loadingOverlay').hide();

                if (response.success) {
                    // Store the order ID for later use
                    currentOrderId = response.data.id;

                    const actionText = withPrint ? 'KOT sent to kitchen and printed!' : 'KOT sent to kitchen!';

                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: actionText,
                        confirmButtonText: 'OK'
                    }).then(() => {
                        // Don't clear the order - keep it for bill/payment
                        // Just disable KOT buttons and enable Bill/Pay buttons
                        $('#kotBtn, #kotPrintBtn').prop('disabled', true);
                        $('#billBtn, #payBtn').prop('disabled', false);
                    });
                } else {
                    Swal.fire('Error', response.message || 'Failed to send KOT', 'error');
                }
            },
            error: function(xhr) {
                $('#loadingOverlay').hide();

                let errorMessage = 'Failed to send KOT';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join('\n');
                }

                Swal.fire('Error', errorMessage, 'error');
            }
        });
    }

    function generateBill() {
        if (!currentOrderId) {
            Swal.fire('Error', 'Please send KOT first before generating bill', 'error');
            return;
        }

        // Show loading overlay
        $('#loadingOverlay').show();

        // Generate and print bill
        $.ajax({
            url: `/pos/orders/${currentOrderId}/bill`,
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Accept': 'application/json'
            },
            success: function(response) {
                $('#loadingOverlay').hide();

                if (response.success) {
                    // Open bill in new window for printing
                    const billWindow = window.open('', '_blank');
                    billWindow.document.write(response.bill_html);
                    billWindow.document.close();
                    billWindow.print();

                    Swal.fire({
                        icon: 'success',
                        title: 'Bill Generated!',
                        text: 'Bill has been generated and sent to printer',
                        confirmButtonText: 'OK'
                    });
                } else {
                    Swal.fire('Error', response.message || 'Failed to generate bill', 'error');
                }
            },
            error: function(xhr) {
                $('#loadingOverlay').hide();
                Swal.fire('Error', 'Failed to generate bill', 'error');
            }
        });
    }

    function openPaymentInterface() {
        if (!currentOrderId) {
            Swal.fire('Error', 'Please send KOT first before processing payment', 'error');
            return;
        }

        // Redirect to payment interface
        window.location.href = `/pos/orders/${currentOrderId}/payment`;
    }
});
</script>
@endpush

@endsection