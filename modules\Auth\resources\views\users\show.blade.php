@extends('layouts.master')

@section('title', 'عرض المستخدم')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0 font-size-18">عرض المستخدم</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">المستخدمين</a></li>
                        <li class="breadcrumb-item active">عرض المستخدم</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- User Details Card -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">تفاصيل المستخدم</h4>
                        <div>
                            <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-primary btn-sm me-2">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- User Avatar -->
                        <div class="col-md-3 text-center mb-4">
                            <div class="avatar-lg mx-auto mb-3">
                                @if($user->avatar)
                                    <img src="{{ asset('storage/' . $user->avatar) }}" alt="{{ $user->name }}" 
                                         class="rounded-circle img-thumbnail" style="width: 120px; height: 120px; object-fit: cover;">
                                @else
                                    <div class="avatar-title rounded-circle bg-primary text-white" style="width: 120px; height: 120px; font-size: 48px; line-height: 120px;">
                                        {{ strtoupper(substr($user->name, 0, 1)) }}
                                    </div>
                                @endif
                            </div>
                            <h5 class="font-size-16 mb-1">{{ $user->name }}</h5>
                            <p class="text-muted mb-2">{{ $user->email }}</p>
                            <div class="mt-3">
                                @if($user->is_active)
                                    <span class="badge bg-success">نشط</span>
                                @else
                                    <span class="badge bg-danger">غير نشط</span>
                                @endif
                            </div>
                        </div>

                        <!-- User Information -->
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">الاسم الكامل:</label>
                                    <p class="text-muted mb-0">{{ $user->name }}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">البريد الإلكتروني:</label>
                                    <p class="text-muted mb-0">{{ $user->email }}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">رقم الهاتف:</label>
                                    <p class="text-muted mb-0">{{ $user->phone ?? 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">المنصب:</label>
                                    <p class="text-muted mb-0">{{ $user->position ?? 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">القسم:</label>
                                    <p class="text-muted mb-0">{{ $user->department ?? 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">الراتب:</label>
                                    <p class="text-muted mb-0">{{ $user->salary ? number_format($user->salary) . ' ريال' : 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                                    <p class="text-muted mb-0">{{ $user->created_at->format('Y-m-d H:i') }}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">آخر تحديث:</label>
                                    <p class="text-muted mb-0">{{ $user->updated_at->format('Y-m-d H:i') }}</p>
                                </div>
                                @if($user->email_verified_at)
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">تاريخ تأكيد البريد:</label>
                                    <p class="text-muted mb-0">{{ $user->email_verified_at->format('Y-m-d H:i') }}</p>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- User Roles -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="font-size-14 mb-3">الأدوار المخصصة:</h5>
                            @if($user->roles && $user->roles->count() > 0)
                                <div class="d-flex flex-wrap gap-2">
                                    @foreach($user->roles as $role)
                                        <span class="badge bg-info font-size-12">{{ $role->name }}</span>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-muted">لا يوجد أدوار مخصصة لهذا المستخدم</p>
                            @endif
                        </div>
                    </div>

                    <!-- User Permissions -->
                    @if($user->getAllPermissions()->count() > 0)
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="font-size-14 mb-3">الصلاحيات:</h5>
                            <div class="d-flex flex-wrap gap-2">
                                @foreach($user->getAllPermissions() as $permission)
                                    <span class="badge bg-secondary font-size-11">{{ $permission->name }}</span>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-title {
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
@endpush